// 认证相关工具函数

// Token 存储键名
const TOKEN_KEY = 'userToken';
const USER_INFO_KEY = 'userInfo';

/**
 * 设置用户登录信息
 * @param {string} token - 用户token
 * @param {object} userInfo - 用户信息
 */
export const setAuth = (token, userInfo) => {
    localStorage.setItem(TOKEN_KEY, token);
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));

    // 触发自定义事件通知其他组件
    window.dispatchEvent(new CustomEvent('userLoginSuccess', {
        detail: { token, userInfo }
    }));
};

/**
 * 获取用户token
 * @returns {string|null} token
 */
export const getToken = () => {
    return localStorage.getItem(TOKEN_KEY);
};

/**
 * 获取用户信息
 * @returns {object|null} 用户信息对象
 */
export const getUserInfo = () => {
    const userInfo = localStorage.getItem(USER_INFO_KEY);
    return userInfo ? JSON.parse(userInfo) : null;
};

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export const isAuthenticated = () => {
    const token = getToken();
    const userInfo = getUserInfo();
    return !!(token && userInfo);
};

/**
 * 清除用户登录信息（退出登录）
 */
export const clearAuth = () => {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(USER_INFO_KEY);

    // 触发自定义事件通知其他组件
    window.dispatchEvent(new CustomEvent('userLogoutSuccess'));
};

/**
 * 模拟登录验证
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @returns {Promise<object>} 登录结果
 */
export const login = async (username, password) => {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟用户数据库
    const mockUsers = [
        {
            id: 1,
            username: 'admin',
            password: '123456',
            name: 'admin',
            role: 'admin',
            email: '<EMAIL>',
            avatar: '/src/assets/images/default-avatar.png'
        },
        {
            id: 2,
            username: 'teacher',
            password: '123456',
            name: '张老师',
            role: 'teacher',
            email: '<EMAIL>',
            avatar: '/src/assets/images/default-avatar.png'
        },
        {
            id: 3,
            username: 'student',
            password: '123456',
            name: '李同学',
            role: 'student',
            email: '<EMAIL>',
            avatar: '/src/assets/images/default-avatar.png'
        }
    ];
    
    // 查找用户
    const user = mockUsers.find(u => u.username === username && u.password === password);
    
    if (user) {
        // 生成模拟token
        const token = `token_${user.id}_${Date.now()}`;
        
        // 用户信息（不包含密码）
        const userInfo = {
            id: user.id,
            username: user.username,
            name: user.name,
            role: user.role,
            email: user.email,
            avatar: user.avatar
        };
        
        return {
            success: true,
            token,
            userInfo,
            message: '登录成功'
        };
    } else {
        return {
            success: false,
            message: '用户名或密码错误'
        };
    }
};

/**
 * 模拟注册
 * @param {object} registerData - 注册数据
 * @returns {Promise<object>} 注册结果
 */
export const register = async (registerData) => {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const { username, email, password } = registerData;
    
    // 简单验证
    if (!username || !email || !password) {
        return {
            success: false,
            message: '请填写完整信息'
        };
    }
    
    if (password.length < 6) {
        return {
            success: false,
            message: '密码长度至少6位'
        };
    }
    
    // 模拟检查用户名是否已存在
    const existingUsers = ['admin', 'teacher', 'student'];
    if (existingUsers.includes(username)) {
        return {
            success: false,
            message: '用户名已存在'
        };
    }
    
    // 模拟注册成功
    return {
        success: true,
        message: '注册成功，请登录'
    };
};

/**
 * 退出登录
 * @returns {Promise<object>} 退出结果
 */
export const logout = async () => {
    // 清除本地存储的认证信息
    clearAuth();
    
    return {
        success: true,
        message: '退出登录成功'
    };
};
